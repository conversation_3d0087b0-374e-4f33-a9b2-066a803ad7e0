<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=Edge,chrome=1" />
    <meta name="format-detection" content="telephone=no" />
    <title>单机游戏_游戏推荐_找游戏_游民众评_游民星空 Gamersky.com</title>
    <meta name="keywords" content="单机游戏,游戏排行榜,游戏搜索与筛选,热门游戏推荐,近期热门游戏,找游戏" />
    <meta name="description" content="游民星空众评为玩家提供完善且优质的游戏筛选、查找功能，同时拥有专业高分游戏和热门游戏推荐等服务，为玩家打造完善的一站式游戏服务体验。" />
    <link rel="stylesheet" href="//j.gamersky.com/css/web2015/ku/css/??style.css,style_screen.css" />
    <script src="//j.gamersky.com/g/jquery-1.8.3.js"></script>
</head>
<body>
        <div class="Top">
  <div class="Headers">
    <div class="logo">
      <a class="logo1" href="https://www.gamersky.com/" target="_blank"></a>
      <i>|</i>
      <a class="logo2" href="https://ku.gamersky.com/"></a>
    </div>
    <div class="sosouser">
      <div class="soso">
        <div class="search" id="search">
          <input class="Sinput" id="kw" type="text" name="s" value="" maxlength="150" />
          <input class="Sbutton" type="button" value="搜索" onclick="var url='https://so.gamersky.com/all/ku?s='+encodeURI(kw.value);window.open(url).location=url;" />
        </div>
        <script>(function(){$("#search").on("keyup","input",function(event){if (event.keyCode == 13) {$(this).parent().find(".Sbutton").click();}});})();</script>
        <div class="nav">
          <a class="alik" href="https://www.gamersky.com/">首页</a>
          <a class="alik" href="https://club.gamersky.com/"  target="_blank">社区</a>
          <span class="navtt">
            <p class="alik">导航</p>
            <div class="topbar-menu url">
              <script src="https://www.gamersky.com/autoinc/allsite/allsite_urlnav_inc.htm"></script>
            </div>
          </span>
          <span class="navtt"><a class="alik tongzhi" href="javascript:;">通知</a></span>
        </div>
      </div>
      <div class="userLogin"></div>
    </div>
  </div>
</div>
<script src="//j.gamersky.com/g/QZuserLogin.js"></script>
<script src="//j.gamersky.com/g/paho-mqtt.js"></script>
<script>
$('body').on('click','a',function(){
if ($(this).attr('href') == 'https://i.gamersky.com/club/') {
  $(this).attr('href','https://club.gamersky.com/');
  $(this).click();
}
});
</script>
    <!-- ParserInfo: Processed in 0.0474251 second(s) Ticks:115784 -->    <ul class="Menu">
        <li>
            <div class="tit">游戏类型：</div>
            <div class="con">
                <div class="zhanbtn" data-state="CLOSE">展开</div>
                <div class="cont">
                                        <a href="/sp/1758-0-0-0-0-0.html" class="cur">全部</a>
                                        <a href="/sp/FPS/1758-0-0-0-0-0.html" class="m">第一人称射击</a>
                                        <a href="/sp/TPS/1758-0-0-0-0-0.html" class="m">第三人称射击</a>
                                        <a href="/sp/ACT/1758-0-0-0-0-0.html" class="m">动作游戏</a>
                                        <a href="/sp/RPG/1758-0-0-0-0-0.html" class="m">角色扮演</a>
                                        <a href="/sp/ARPG/1758-0-0-0-0-0.html" class="m">动作角色扮演</a>
                                        <a href="/sp/RAC/1758-0-0-0-0-0.html" class="m">竞速游戏</a>
                                        <a href="/sp/RTS/1758-0-0-0-0-0.html" class="m">即时战略</a>
                                        <a href="/sp/SLG/1758-0-0-0-0-0.html" class="m">策略游戏</a>
                                        <a href="/sp/AVG/1758-0-0-0-0-0.html" class="m">冒险游戏</a>
                                        <a href="/sp/SPG/1758-0-0-0-0-0.html" class="m">体育游戏</a>
                                        <a href="/sp/SIM/1758-0-0-0-0-0.html" class="m">模拟游戏</a>
                                        <a href="/sp/FTG/1758-0-0-0-0-0.html" class="m">格斗游戏</a>
                                        <a href="/sp/AS/1758-0-0-0-0-0.html" class="m">飞行射击</a>
                                        <a href="/sp/PUZ/1758-0-0-0-0-0.html" class="m">益智游戏</a>
                                        <a href="/sp/MUSIC/1758-0-0-0-0-0.html" class="m">音乐游戏</a>
                                        <a href="/sp/GAL/1758-0-0-0-0-0.html" class="m">恋爱养成</a>
                                    </div>
            </div>
        </li>
        <li>
            <div class="tit">游戏平台：</div>
            <div class="con">
                <div class="zhanbtn" data-state="CLOSE">展开</div>
                <div class="cont">
                    <a href="/sp/" class="m">全部</a>
                                                                                                                                                                                                                                                                                                                                <a href="/sp/1751-0-0-0-0-0.html" class="m">PC</a>
                                                                                                                                                                <a href="/sp/5916-0-0-0-0-0.html" class="m">Xbox Series X</a>
                                                                                <a href="/sp/1760-0-0-0-0-0.html" class="m">Xbox One</a>
                                                                                <a href="/sp/5915-0-0-0-0-0.html" class="m">PS5</a>
                                                                                <a href="/sp/1758-0-0-0-0-0.html" class="cur">PS4</a>
                                                                                <a href="/sp/1752-0-0-0-0-0.html" class="m">Xbox 360</a>
                                                                                <a href="/sp/1759-0-0-0-0-0.html" class="m">PS3</a>
                                                                                <a href="/sp/4146-0-0-0-0-0.html" class="m">Switch</a>
                                                                                <a href="/sp/15245-0-0-0-0-0.html" class="m">Switch2</a>
                                                                                <a href="/sp/1757-0-0-0-0-0.html" class="m">Wii U</a>
                                                                                <a href="/sp/1756-0-0-0-0-0.html" class="m">PS Vita</a>
                                                                                <a href="/sp/1755-0-0-0-0-0.html" class="m">3DS</a>
                                                                                                                                                                                                                                                                                                                                                                                                                <a href="/sp/1753-0-0-0-0-0.html" class="m">iOS</a>
                                                                                <a href="/sp/1754-0-0-0-0-0.html" class="m">Android</a>
                                                                                                                                                                                </div>
            </div>
        </li>
        <li>
            <div class="tit">发售时间：</div>
            <div class="con">
                <div class="zhanbtn" data-state="CLOSE">展开</div>
                <div class="cont">
                    <a href="/sp/1758-0-0-0-0-0.html" class="cur">全部</a>
                    <a href="/sp/1758-0-99999-0-0-0.html" class="m">2025以后</a>
                                        <a href="/sp/1758-0-2025-0-0-0.html" class="m">2025</a>
                                        <a href="/sp/1758-0-2024-0-0-0.html" class="m">2024</a>
                                        <a href="/sp/1758-0-2023-0-0-0.html" class="m">2023</a>
                                        <a href="/sp/1758-0-2022-0-0-0.html" class="m">2022</a>
                                        <a href="/sp/1758-0-2021-0-0-0.html" class="m">2021</a>
                                        <a href="/sp/1758-0-2020-0-0-0.html" class="m">2020</a>
                                        <a href="/sp/1758-0-2019-0-0-0.html" class="m">2019</a>
                                        <a href="/sp/1758-0-2018-0-0-0.html" class="m">2018</a>
                                        <a href="/sp/1758-0-2017-0-0-0.html" class="m">2017</a>
                                        <a href="/sp/1758-0-2016-0-0-0.html" class="m">2016</a>
                                        <a href="/sp/1758-0-2015-0-0-0.html" class="m">2015</a>
                                        <a href="/sp/1758-0-2014-0-0-0.html" class="m">2014</a>
                                        <a href="/sp/1758-0-2013-0-0-0.html" class="m">2013</a>
                                        <a href="/sp/1758-0-2012-0-0-0.html" class="m">2012</a>
                                        <a href="/sp/1758-0-2011-0-0-0.html" class="m">2011</a>
                                        <a href="/sp/1758-0-2010-0-0-0.html" class="m">2010</a>
                                        <a href="/sp/1758-0-2009-0-0-0.html" class="m">2009</a>
                                        <a href="/sp/1758-0-2008-0-0-0.html" class="m">2008</a>
                                        <a href="/sp/1758-0-2007-0-0-0.html" class="m">2007</a>
                                        <a href="/sp/1758-0-2006-0-0-0.html" class="m">2006</a>
                                        <a href="/sp/1758-0-2005-0-0-0.html" class="m">2005</a>
                                        <a href="/sp/1758-0-2004-0-0-0.html" class="m">2004</a>
                                        <a href="/sp/1758-0-2003-0-0-0.html" class="m">2003</a>
                                        <a href="/sp/1758-0-92002-0-0-0.html" class="m">更早</a>
                </div>
            </div>
        </li>
        <li>
            <div class="tit">游戏厂商：</div>
            <div class="con">
                <div class="zhanbtn" data-state="CLOSE">展开</div>
                <div class="cont">
                    <a href="/sp/1758-0-0-0-0-0.html" class="cur">全部</a>
                                                                                                                        <a href="/sp/1758-2-0-0-0-0.html" class="m">2K Games</a>
                                                                                <a href="/sp/1758-3-0-0-0-0.html" class="m">505 Games</a>
                                                                                <a href="/sp/1758-4-0-0-0-0.html" class="m">雅达利</a>
                                                                                <a href="/sp/1758-5-0-0-0-0.html" class="m">动视</a>
                                                                                <a href="/sp/1758-6-0-0-0-0.html" class="m">Atlus</a>
                                                                                <a href="/sp/1758-7-0-0-0-0.html" class="m">Bethesda</a>
                                                                                <a href="/sp/1758-8-0-0-0-0.html" class="m">BioWare</a>
                                                                                <a href="/sp/1758-9-0-0-0-0.html" class="m">暴雪</a>
                                                                                <a href="/sp/1758-10-0-0-0-0.html" class="m">卡普空</a>
                                                                                <a href="/sp/1758-11-0-0-0-0.html" class="m">Codemasters</a>
                                                                                <a href="/sp/1758-12-0-0-0-0.html" class="m">City Interactive</a>
                                                                                <a href="/sp/1758-13-0-0-0-0.html" class="m">CD Projekt</a>
                                                                                <a href="/sp/1758-14-0-0-0-0.html" class="m">Deep Silver</a>
                                                                                <a href="/sp/1758-15-0-0-0-0.html" class="m">迪士尼</a>
                                                                                <a href="/sp/1758-16-0-0-0-0.html" class="m">EA</a>
                                                                                <a href="/sp/1758-17-0-0-0-0.html" class="m">Epic</a>
                                                                                <a href="/sp/1758-18-0-0-0-0.html" class="m">Eidos</a>
                                                                                <a href="/sp/1758-19-0-0-0-0.html" class="m">Konami</a>
                                                                                <a href="/sp/1758-20-0-0-0-0.html" class="m">LucasArts</a>
                                                                                <a href="/sp/1758-21-0-0-0-0.html" class="m">Level 5</a>
                                                                                <a href="/sp/1758-22-0-0-0-0.html" class="m">微软</a>
                                                                                <a href="/sp/1758-23-0-0-0-0.html" class="m">Namco Bandai</a>
                                                                                <a href="/sp/1758-24-0-0-0-0.html" class="m">NCsoft</a>
                                                                                <a href="/sp/1758-25-0-0-0-0.html" class="m">Nexon</a>
                                                                                <a href="/sp/1758-26-0-0-0-0.html" class="m">Nintendo</a>
                                                                                <a href="/sp/1758-27-0-0-0-0.html" class="m">Paradox</a>
                                                                                <a href="/sp/1758-28-0-0-0-0.html" class="m">Rockstar</a>
                                                                                <a href="/sp/1758-29-0-0-0-0.html" class="m">索尼</a>
                                                                                <a href="/sp/1758-30-0-0-0-0.html" class="m">世嘉</a>
                                                                                <a href="/sp/1758-31-0-0-0-0.html" class="m">史克威尔艾尼克斯</a>
                                                                                <a href="/sp/1758-32-0-0-0-0.html" class="m">THQ</a>
                                                                                <a href="/sp/1758-33-0-0-0-0.html" class="m">育碧</a>
                                                                                <a href="/sp/1758-34-0-0-0-0.html" class="m">Valve</a>
                                                                                <a href="/sp/1758-35-0-0-0-0.html" class="m">华纳兄弟</a>
                                                                                <a href="/sp/1758-36-0-0-0-0.html" class="m">KOEI TECMO</a>
                                                                                <a href="/sp/1758-37-0-0-0-0.html" class="m">Falcom</a>
                                                                                <a href="/sp/1758-38-0-0-0-0.html" class="m">百游</a>
                                                                                <a href="/sp/1758-39-0-0-0-0.html" class="m">GameBar</a>
                                                                                <a href="/sp/1758-40-0-0-0-0.html" class="m">腾讯游戏</a>
                                                                                <a href="/sp/1758-41-0-0-0-0.html" class="m">盛大游戏</a>
                                                                                <a href="/sp/1758-42-0-0-0-0.html" class="m">网易游戏</a>
                                                                                <a href="/sp/1758-43-0-0-0-0.html" class="m">完美世界</a>
                                                                                <a href="/sp/1758-44-0-0-0-0.html" class="m">巨人网络</a>
                                                                                <a href="/sp/1758-45-0-0-0-0.html" class="m">世纪天成</a>
                                                                                <a href="/sp/1758-46-0-0-0-0.html" class="m">第九城市</a>
                                                                                <a href="/sp/1758-47-0-0-0-0.html" class="m">金山游戏</a>
                                                                                <a href="/sp/1758-48-0-0-0-0.html" class="m">搜狐畅游</a>
                                                        </div>
            </div>
            <div class="tit">官方中文：</div>
            <div class="zhanbtn" data-state="CLOSE">展开</div>
            <div class="con">
                <div class="cont">
                    <a href="/sp/1758-0-0-0-0-0.html" class="cur">全部</a>
                    <a href="/sp/1758-0-0-0-0-1.html" class="m">支持</a>
                    <a href="/sp/1758-0-0-0-0-2.html" class="m">不支持</a>
                </div>
            </div>
        </li>
        <li>
            <div class="tit">游戏标签：</div>
            <div class="con">
                <div class="zhanbtn" data-state="CLOSE">展开</div>
                <div class="cont">
                    <a href="/sp/1758-0-0-0-0-0.html" class="cur">全部</a>
                                                            <a href="/sp/1758-0-0-3-0-0.html" class="m">国产</a>
                                        <a href="/sp/1758-0-0-12-0-0.html" class="m">独立游戏</a>
                                        <a href="/sp/1758-0-0-54-0-0.html" class="m">硬件杀手</a>
                                        <a href="/sp/1758-0-0-38-0-0.html" class="m">二战</a>
                                        <a href="/sp/1758-0-0-10-0-0.html" class="m">现代战争</a>
                                        <a href="/sp/1758-0-0-32-0-0.html" class="m">未来战争</a>
                                        <a href="/sp/1758-0-0-29-0-0.html" class="m">丧尸</a>
                                        <a href="/sp/1758-0-0-5-0-0.html" class="m">科幻</a>
                                        <a href="/sp/1758-0-0-13-0-0.html" class="m">奇幻</a>
                                        <a href="/sp/1758-0-0-6-0-0.html" class="m">恐怖</a>
                                        <a href="/sp/1758-0-0-36-0-0.html" class="m">末世</a>
                                        <a href="/sp/1758-0-0-4-0-0.html" class="m">剑侠</a>
                                        <a href="/sp/1758-0-0-47-0-0.html" class="m">漫画英雄</a>
                                        <a href="/sp/1758-0-0-45-0-0.html" class="m">赛博朋克</a>
                                        <a href="/sp/1758-0-0-51-0-0.html" class="m">蒸汽朋克</a>
                                        <a href="/sp/1758-0-0-25-0-0.html" class="m">回合制</a>
                                        <a href="/sp/1758-0-0-27-0-0.html" class="m">沙盒</a>
                                        <a href="/sp/1758-0-0-14-0-0.html" class="m">横版卷轴</a>
                                        <a href="/sp/1758-0-0-11-0-0.html" class="m">潜入暗杀</a>
                                        <a href="/sp/1758-0-0-8-0-0.html" class="m">改编</a>
                                        <a href="/sp/1758-0-0-1-0-0.html" class="m">忍者</a>
                                        <a href="/sp/1758-0-0-15-0-0.html" class="m">吸血鬼</a>
                                    </div>
            </div>
        </li>
    </ul><!-- Menu end -->

    <div class="Mid">
                <div class="Mid-top">
            <div class="sort">
                <span>排序方式：</span>
                <a href="javascript:CreateCookie('00')" class="cur desc">热门排序</a>
                <a href="javascript:CreateCookie('10')" class="m ">时间排序</a>
                                <a href="javascript:CreateCookie('20')" class="m ">评分排序</a>
                            </div>
            <div class="right">
                <div class="select">
                    <div class="txt"><span>全部</span><i></i></div>
                    <div class="con">
                        <a href="/sp/1758-0-0-0-0-0.html">全部</a>
                        <a href="/sp/1758-0-0-0-2-0.html">已上市</a>
                        <a href="/sp/1758-0-0-0-1-0.html">未上市</a>
                    </div>
                </div>
                <div class="num">共<span>4989</span>款游戏</div>
            </div>
        </div>
        <ul class="imglist pageContainer" data-nodeid="20039" data-count="4989" data-pagesize="36"></ul>
        <div class="loading"><i></i><span>正在加载...</span></div>
        <div class="loadend"><img alt="已加载完所有信息" src="//image.gamersky.com/webimg13/zl/academy/done.gif" /></div>
        <div class="Page contentpage" style="display: none;">
            <!--{pe.begin.pagination}-->
            <span id="pe100_page_pictxt" class="pagecss">
                <a href="javascript:void(0)" class="p1 previous" style="display: inline-block;">上一页</a>
                <a href="javascript:void(0)" class="p1 nexe">下一页</a>
            </span>
            <!--{pe.end.pagination}-->
        </div>
    </div><!--Mid end-->

    <div class="Bot">
        <div class="Hot">
            <div class="Hots">
                <script src="https://www.gamersky.com/autoinc/allsite/bottom_link_js.htm"></script>
            </div><!--Hots end-->
        </div><!--Hot end-->
        <script src="https://www.gamersky.com/autoinc/allsite/allsite_copyright_1_js.htm"></script>
    </div><!--Bot end-->

    <script src="//j.gamersky.com/g/jquery.gamersky.v4.min.js"></script>
    <script src="//j.gamersky.com/js/web2015/ku/js/??zp_screen.js,scrollloadingpage.js"></script>

        <!-- 20180516 update -->
<script src="//ja2.gamersky.com/gs.tg.all.js?2023113016"></script>
<script src="//j.gamersky.com/common/tg/fxtg.js?20240223"></script>
<script src="//j.gamersky.com/common/tg/allsite.tg.monitor.js"></script>
<!--[if lt IE 8]>
<script>
(function(b){b("img").each(function(){var c=b(this),a=c.attr("src");0<=a.indexOf("https://")&&(a=a.replace("https://","http://"),c.attr("src",a))})})(jQuery);
</script>
<![endif]-->
<div style="display:none">
<script>document.write(unescape("%3Cscript src='//hm.baidu.com/h.js%3Fdcb5060fba0123ff56d253331f28db6a' %3E%3C/script%3E"));</script>
<!-- Google 统计PC -->
<script async src="https://www.googletagmanager.com/gtag/js?id=G-H94H6VYZY1"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'G-H94H6VYZY1');
</script>
</div>
    <!-- ParserInfo: Processed in 0.027215 second(s) Ticks:66443 --></body>
</html><!-- ParserInfo: Processed in 0.0212893 second(s) Ticks:51976 -->